from PyQt5.QtWidgets import (QApplication, QFileDialog, QMainWindow, 
                            QVBoxLayout, QWidget, QTextEdit, QPushButton,
                            QLabel, QHBoxLayout, QScrollArea)
from PyQt5.QtCore import Qt
import pandas as pd
import sys
import os

class ExcelAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        
    def initUI(self):
        self.setWindowTitle('Excel/CSV数据分析工具')
        self.setGeometry(300, 300, 800, 600)
        
        # 主部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 主布局
        layout = QVBoxLayout()
        main_widget.setLayout(layout)
        
        # 文件选择按钮
        self.btn_open = QPushButton('选择Excel/CSV文件')
        self.btn_open.clicked.connect(self.open_file)
        layout.addWidget(self.btn_open)
        
        # 结果显示区域
        self.result_area = QTextEdit()
        self.result_area.setReadOnly(True)
        
        # 复制按钮
        self.btn_copy = QPushButton('复制结果')
        self.btn_copy.clicked.connect(self.copy_results)
        self.btn_copy.setEnabled(False)
        
        # 滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setWidget(self.result_area)
        
        layout.addWidget(scroll)
        layout.addWidget(self.btn_copy)
        
    def open_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Excel/CSV文件", 
            "", 
            "Excel/CSV文件 (*.xlsx *.xls *.csv);;所有文件 (*.*)"
        )

        if not file_path:
            return
            
        try:
            # 根据文件扩展名选择读取方式
            ext = os.path.splitext(file_path)[1].lower()
            if ext == '.csv':
                df = pd.read_csv(file_path)
            else:  # Excel文件
                df = pd.read_excel(file_path)

            # 分析数据
            results = self.analyze_data(df)
            
            # 显示结果
            self.show_results(results)
            self.btn_copy.setEnabled(True)
            
        except Exception as e:
            self.result_area.setText(f"处理文件时发生错误: {str(e)}")
    
    def analyze_data(self, df):
        duplicates_found = {}
        sample_column = df.columns[0]

        for data_column in df.columns[1:]:
            duplicated_rows = df[df.duplicated(subset=[data_column], keep=False)]
            
            if not duplicated_rows.empty:
                grouped = duplicated_rows.groupby(data_column)
                for value, group in grouped:
                    samples = group[sample_column].tolist()
                    if len(samples) > 1:
                        if data_column not in duplicates_found:
                            duplicates_found[data_column] = []
                        duplicates_found[data_column].append({
                            'value': value,
                            'samples': samples
                        })
        return duplicates_found
    
    def show_results(self, results):
        output = []
        if results:
            output.append("### 分析完成：发现具有相同单列理化值的样品\n\n")
            for column_name in sorted(results.keys()):
                output.append(f"**理化值: {column_name}**\n\n")
                for finding in results[column_name]:
                    value = finding['value']
                    sample_list = ", ".join(finding['samples'])
                    output.append(f"- 相同数值: {value}\n")
                    output.append(f"- 对应样品: {sample_list}\n\n")
                output.append("-" * 20 + "\n")
        else:
            output.append("分析完成：在所有单列理化值中均未找到具有完全相同值的样品。")
        
        self.result_area.setText("".join(output))
    
    def copy_results(self):
        clipboard = QApplication.clipboard()
        clipboard.setText(self.result_area.toPlainText())

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = ExcelAnalyzer()
    ex.show()
    sys.exit(app.exec_())
